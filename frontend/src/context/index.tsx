import { createContext, useEffect, useMemo, ReactNode } from "react";
import { useTheme } from "@/stores/userStore";
import { useColorPalette, useSettingsHydrated } from "@/stores/settingsStore";

interface ThemeContextValue {
  customColor: string;
  theme?: string;
}

export const ThemeContext = createContext<ThemeContextValue | undefined>(
  undefined
);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider = ({ children }: ThemeProviderProps) => {
  const theme = useTheme();
  const customColor = useColorPalette();
  const isHydrated = useSettingsHydrated();

  useEffect(() => {
    // Only load CSS after the store has been hydrated to avoid loading default theme
    if (isHydrated) {
      const colorToLoad = customColor || "rwanda";
      loadCustomCSS(colorToLoad);
    }
  }, [customColor, isHydrated]);

  const loadCustomCSS = (color: string) => {
    const existingLink = document.getElementById("custom-css");
    if (existingLink) {
      existingLink.remove();
    }

    const link = document.createElement("link");
    link.id = "custom-css";
    link.rel = "stylesheet";
    link.href = `/${color}index.css`;
    document.head.appendChild(link);
  };

  const contextValue = useMemo<ThemeContextValue>(
    () => ({
      customColor: isHydrated ? customColor || "rwanda" : "rwanda",
      theme,
    }),
    [customColor, theme, isHydrated]
  );

  return (
    <ThemeContext.Provider value={contextValue}>
      <div
        id="theme-wrapper"
        className={theme === "dark" ? "dark-theme" : "light-theme"}
      >
        {children}
      </div>
    </ThemeContext.Provider>
  );
};
