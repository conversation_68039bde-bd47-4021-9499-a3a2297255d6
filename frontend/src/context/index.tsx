import { createContext, useEffect, useMemo, useState, ReactNode } from "react";
import { useTheme } from "@/stores/userStore";
import { useColorPalette, useSettingsHydrated } from "@/stores/settingsStore";

interface ThemeContextValue {
  customColor: string;
  theme?: string;
}

export const ThemeContext = createContext<ThemeContextValue | undefined>(
  undefined
);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider = ({ children }: ThemeProviderProps) => {
  const theme = useTheme();
  const customColor = useColorPalette();
  const isHydrated = useSettingsHydrated();
  const [hasLoadedInitialCSS, setHasLoadedInitialCSS] = useState(false);

  const loadCustomCSS = (color: string) => {
    const existingLink = document.getElementById("custom-css");
    if (existingLink) {
      existingLink.remove();
    }

    const link = document.createElement("link");
    link.id = "custom-css";
    link.rel = "stylesheet";
    link.href = `/${color}index.css`;
    document.head.appendChild(link);
  };

  useEffect(() => {
    // Only load CSS after the store has been hydrated to avoid loading default theme
    if (isHydrated && !hasLoadedInitialCSS) {
      const colorToLoad = customColor || "rwanda";
      loadCustomCSS(colorToLoad);
      setHasLoadedInitialCSS(true);
    } else if (isHydrated && hasLoadedInitialCSS && customColor) {
      // Update CSS when color changes after initial load
      loadCustomCSS(customColor);
    }
  }, [customColor, isHydrated, hasLoadedInitialCSS]);

  const contextValue = useMemo<ThemeContextValue>(
    () => ({
      customColor: isHydrated ? customColor || "rwanda" : "rwanda",
      theme,
    }),
    [customColor, theme, isHydrated]
  );

  return (
    <ThemeContext.Provider value={contextValue}>
      <div
        id="theme-wrapper"
        className={theme === "dark" ? "dark-theme" : "light-theme"}
      >
        {children}
      </div>
    </ThemeContext.Provider>
  );
};
