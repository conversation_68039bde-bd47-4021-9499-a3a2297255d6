/**
 * System Settings Store
 *
 * Centralized state management for all system-wide settings.
 * Uses Zustand with persistence to manage and cache settings data.
 *
 * Features:
 * - Automatic caching with configurable refresh intervals
 * - Batch updates for performance optimization
 * - Type-safe setting accessors via custom hooks
 * - Automatic retry and error handling
 *
 * @module settingsStore
 */

import { API_BASE } from "@/utils/constants";
import { create } from "zustand";
import { baseHeaders } from "@/utils/request";
import { persist } from "zustand/middleware";

interface TabNames {
  tabName1: string;
  tabName2: string;
  tabName3: string;
}

interface DocumentDraftingSettings {
  isDocumentDrafting: boolean;
  isDocumentDraftingLinking: boolean;
}

interface RequestLegalAssistanceSettings {
  enabled: boolean;
  lawFirmName: string;
  email: string;
}

interface FeedbackSettings {
  enabled: boolean;
}

interface SlackSettings {
  enabled: boolean;
  bugReportWebhookUrl: string;
  autocodingWebhookUrl: string;
  instanceName: string;
  hasEnvWebhook: boolean;
  webhookUrl?: string; // Legacy property for backward compatibility
}

type SettingValue =
  | TabNames
  | DocumentDraftingSettings
  | RequestLegalAssistanceSettings
  | FeedbackSettings
  | SlackSettings
  | boolean
  | string;

interface EndpointConfig {
  endpoint: string;
  updateEndpoint: string | null;
  defaultValue: SettingValue;
}

const ENDPOINTS: Record<string, EndpointConfig> = {
  "tab-names": {
    endpoint: "/get-tab-names",
    updateEndpoint: "/system/custom-tab-names",
    defaultValue: {
      tabName1: "Legal Q&A",
      tabName2: "Document Drafting",
      tabName3: "Settings",
    },
  },
  "force-invoice-logging": {
    endpoint: "/system/force-invoice-logging",
    updateEndpoint: "/system/force-invoice-logging",
    defaultValue: false,
  },
  "invoice-logging": {
    endpoint: "/system/invoice-logging",
    updateEndpoint: "/system/invoice-logging",
    defaultValue: false,
  },
  "rexor-linkage": {
    endpoint: "/system/rexor-linkage",
    updateEndpoint: "/system/rexor-linkage",
    defaultValue: false,
  },
  "feedback-enabled": {
    endpoint: "/system/feedback-enabled",
    updateEndpoint: "/system/feedback-enabled",
    defaultValue: {
      enabled: false,
    },
  },
  "document-drafting": {
    endpoint: "/system/document-drafting",
    updateEndpoint: "/system/set-document-drafting",
    defaultValue: {
      isDocumentDrafting: false,
      isDocumentDraftingLinking: false,
    },
  },
  "request-legal-assistance": {
    endpoint: "/system/request-legal-assistance",
    updateEndpoint: "/system/request-legal-assistance",
    defaultValue: {
      enabled: false,
      lawFirmName: "",
      email: "",
    },
  },
  "color-palette": {
    endpoint: "/admin/system-preferences",
    updateEndpoint: "/admin/system-preferences",
    defaultValue: "",
  },
  "university-mode": {
    endpoint: "/system/preferences?labels=university_mode",
    updateEndpoint: "/system/preferences",
    defaultValue: false,
  },
  "system-language": {
    endpoint: "/setup-complete",
    updateEndpoint: null,
    defaultValue: "en",
  },
  "slack-settings": {
    endpoint: "/system/slack-settings",
    updateEndpoint: "/system/slack-settings",
    defaultValue: {
      enabled: false,
      bugReportWebhookUrl: "",
      autocodingWebhookUrl: "",
      instanceName: "",
      hasEnvWebhook: false,
    },
  },
  customAppName: {
    endpoint: "/admin/system-preferences",
    updateEndpoint: "/admin/system-preferences",
    defaultValue: "",
  },
};

const DEFAULT_REFRESH_INTERVAL = 12 * 60 * 60 * 1000;

/**
 * Debounce utility for batch updates
 */
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): T => {
  let timeout: NodeJS.Timeout | undefined;
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  } as T;
};

/**
 * Transform data payload based on setting key
 */
const transformPayload = (key: string, data: any): any => {
  switch (key) {
    case "document-drafting":
      return {
        document_drafting: data?.isDocumentDrafting ?? false,
        document_drafting_linking: data?.isDocumentDraftingLinking ?? false,
      };
    case "color-palette":
      return { palette: data };
    case "university-mode":
      return { university_mode: String(data) };
    case "invoice-logging":
      return data?.invoice !== undefined ? data : { invoice: data };
    case "force-invoice-logging":
      return data?.isForcedinvoiceLogging !== undefined
        ? data
        : { isForcedinvoiceLogging: data };
    case "rexor-linkage":
      return data?.rexorLinkage !== undefined ? data : { rexorLinkage: data };
    case "feedback-enabled":
      return data?.enabled !== undefined ? data : { enabled: data };
    default:
      return data;
  }
};

/**
 * Extract local value from data based on setting key
 */
const extractLocalValue = (key: string, data: any): any => {
  switch (key) {
    case "invoice-logging":
      return data?.invoice !== undefined ? data.invoice : data;
    case "force-invoice-logging":
      return data?.isForcedinvoiceLogging !== undefined
        ? data.isForcedinvoiceLogging
        : data;
    case "rexor-linkage":
      return data?.rexorLinkage !== undefined ? data.rexorLinkage : data;
    case "feedback-enabled":
      return data?.enabled !== undefined ? data.enabled : data;
    default:
      return data;
  }
};

interface PendingUpdates {
  values: Record<string, SettingValue>;
  lastFetched: Record<string, number>;
}

interface SettingsState {
  values: Record<string, SettingValue>;
  lastFetched: Record<string, number>;
  loading: Record<string, boolean>;
  errors: Record<string, string | null>;
  refreshInterval: number;
  pendingUpdates: PendingUpdates;
  _hasHydrated: boolean;
  fetchAllSettings: (force?: boolean) => Promise<Record<string, SettingValue>>;
  getSetting: (key: string) => SettingValue | null;
  updateSetting: (key: string, data: any) => Promise<boolean>;
  isLoading: (key: string) => boolean;
  getError: (key: string) => string | null;
  initializeSetting: (key: string, value: SettingValue) => void;
  clearAllSettings: () => void;
  setMultipleSettings: (settings: Record<string, any>) => void;
  setTabNames: (tabNames: TabNames) => void;
  setSetting: (key: string, value: SettingValue) => void;
  setHasHydrated: (state: boolean) => void;
}

interface FetchResult {
  key: string;
  data?: any;
  error?: string;
  success: boolean;
  cached?: boolean;
}

const useSystemSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => {
      // Create debounced batch update function
      const debouncedBatchUpdate = debounce((updates: PendingUpdates) => {
        set((state) => ({
          values: { ...state.values, ...updates.values },
          lastFetched: { ...state.lastFetched, ...updates.lastFetched },
          pendingUpdates: { values: {}, lastFetched: {} }, // Clear pending updates after applying
          _hasHydrated: true, // Mark as hydrated when batch updates are applied
        }));
      }, 300);

      return {
        values: Object.fromEntries(
          Object.entries(ENDPOINTS).map(([key, config]) => [
            key,
            config.defaultValue,
          ])
        ),
        lastFetched: {},
        loading: {},
        errors: {},
        refreshInterval: DEFAULT_REFRESH_INTERVAL,
        pendingUpdates: { values: {}, lastFetched: {} },
        _hasHydrated: false,

        /**
         * Batch update multiple settings with debouncing
         * @param {Object} settings - Key-value pairs of settings to update
         */
        setMultipleSettings: (settings: Record<string, any>) => {
          const updates: PendingUpdates = { values: {}, lastFetched: {} };
          const now = Date.now();

          Object.entries(settings).forEach(([key, value]) => {
            let storeKey = key;

            // Map settings to our store structure if needed
            if (key === "tabNames" && typeof value === "object") {
              updates.values["tab-names"] = value;
              updates.lastFetched["tab-names"] = now;
            } else if (key === "palette") {
              storeKey = "color-palette";
              updates.values[storeKey] = value;
              updates.lastFetched[storeKey] = now;
            } else if (key === "appName") {
              storeKey = "customAppName";
              updates.values[storeKey] = value;
              updates.lastFetched[storeKey] = now;
            } else {
              // Store with original key
              updates.values[key] = value;
              updates.lastFetched[key] = now;
            }
          });

          // Accumulate pending updates and mark as hydrated immediately
          const currentPending = get().pendingUpdates;
          set({
            pendingUpdates: {
              values: { ...currentPending.values, ...updates.values },
              lastFetched: {
                ...currentPending.lastFetched,
                ...updates.lastFetched,
              },
            },
            _hasHydrated: true, // Mark as hydrated immediately when settings are updated
          });

          // Trigger debounced update
          debouncedBatchUpdate(get().pendingUpdates);
        },

        /**
         * Update tab names settings
         * @param {Object} tabNames - Object containing tabName1, tabName2, tabName3
         */
        setTabNames: (tabNames: TabNames) => {
          set((state) => {
            const newValues = { ...state.values };
            const newLastFetched = { ...state.lastFetched };
            const now = Date.now();

            // Store individual tab names
            Object.entries(tabNames).forEach(([key, value]) => {
              newValues[key] = value;
              newLastFetched[key] = now;
            });

            // Also store as tab-names object
            newValues["tab-names"] = tabNames;
            newLastFetched["tab-names"] = now;

            return {
              values: newValues,
              lastFetched: newLastFetched,
            };
          });
        },

        /**
         * Set a single setting value in local state
         * @param {string} key - Setting key
         * @param {*} value - Setting value
         */
        setSetting: (key: string, value: SettingValue) => {
          set((state) => ({
            values: {
              ...state.values,
              [key]: value,
            },
            lastFetched: {
              ...state.lastFetched,
              [key]: Date.now(),
            },
          }));
        },

        /**
         * Fetch all settings from the API
         * @param {boolean} force - Force refresh even if cached
         * @returns {Promise<Object>} Current values after fetch
         */
        fetchAllSettings: async (force = false) => {
          const keys = Object.keys(ENDPOINTS);
          const { lastFetched, refreshInterval } = get();
          const now = Date.now();

          const keysToFetch = force
            ? keys
            : keys.filter((key) => {
                const timestamp = lastFetched[key] || 0;
                return !timestamp || now - timestamp > refreshInterval;
              });

          if (keysToFetch.length === 0) return get().values;

          set((state) => {
            const newLoading = { ...state.loading };
            keysToFetch.forEach((key) => {
              newLoading[key] = true;
            });
            return { loading: newLoading };
          });

          const results = await Promise.all(
            keysToFetch.map(async (key): Promise<FetchResult> => {
              try {
                const { endpoint } = ENDPOINTS[key];
                const res = await fetch(`${API_BASE}${endpoint}`, {
                  method: "GET",
                  headers: baseHeaders(),
                });

                if (!res.ok) {
                  throw new Error(`Failed to fetch ${key}: ${res.statusText}`);
                }

                const data = await res.json();
                return { key, data, success: true };
              } catch (error: any) {
                console.error(`Error fetching setting ${key}:`, error);
                // Return the cached value if available on error
                const cachedValue = get().values[key];
                if (cachedValue !== undefined) {
                  console.error(
                    `Using cached value for ${key} due to fetch error`
                  );
                  return {
                    key,
                    data: cachedValue,
                    success: true,
                    cached: true,
                  };
                }
                return {
                  key,
                  error: error.message || "Network error",
                  success: false,
                };
              }
            })
          );

          set((state) => {
            const newValues = { ...state.values };
            const newLoading = { ...state.loading };
            const newErrors = { ...state.errors };
            const newLastFetched = { ...state.lastFetched };

            results.forEach(({ key, data, error, success }) => {
              newLoading[key] = false;

              if (success) {
                if (key === "color-palette" && data && data.settings) {
                  newValues[key] =
                    data.settings.palette ?? ENDPOINTS[key].defaultValue;
                } else if (key === "university-mode" && data && data.settings) {
                  newValues[key] = data.settings.university_mode === "true";
                } else if (key === "system-language" && data && data.results) {
                  newValues[key] =
                    data.results.language ?? ENDPOINTS[key].defaultValue;
                } else if (key === "invoice-logging" && data) {
                  newValues[key] = data.invoice ?? ENDPOINTS[key].defaultValue;
                } else if (key === "force-invoice-logging" && data) {
                  newValues[key] =
                    data.isForcedinvoiceLogging ?? ENDPOINTS[key].defaultValue;
                } else if (key === "rexor-linkage" && data) {
                  newValues[key] =
                    data.rexorLinkage ?? ENDPOINTS[key].defaultValue;
                } else if (key === "slack-settings" && data && data.settings) {
                  newValues[key] = {
                    enabled: data.settings.enabled ?? false,
                    bugReportWebhookUrl:
                      data.settings.bugReportWebhookUrl ?? "",
                    autocodingWebhookUrl:
                      data.settings.autocodingWebhookUrl ?? "",
                    instanceName: data.settings.instanceName ?? "",
                    hasEnvWebhook: data.settings.hasEnvWebhook ?? false,
                  };
                } else if (key === "tab-names" && data) {
                  const defaultTabNames = ENDPOINTS[key]
                    .defaultValue as TabNames;
                  newValues[key] = {
                    tabName1:
                      data.tabName1 && data.tabName1.trim() !== ""
                        ? data.tabName1
                        : defaultTabNames.tabName1,
                    tabName2:
                      data.tabName2 && data.tabName2.trim() !== ""
                        ? data.tabName2
                        : defaultTabNames.tabName2,
                    tabName3:
                      data.tabName3 && data.tabName3.trim() !== ""
                        ? data.tabName3
                        : defaultTabNames.tabName3,
                  };
                } else if (key === "customAppName" && data && data.settings) {
                  newValues[key] = data.settings.appName ?? "";
                } else {
                  newValues[key] = data;
                }
                newLastFetched[key] = now;
                newErrors[key] = null;
              } else {
                newErrors[key] = error || null;
              }
            });

            return {
              values: newValues,
              loading: newLoading,
              errors: newErrors,
              lastFetched: newLastFetched,
            };
          });

          return get().values;
        },

        getSetting: (key: string) => {
          if (!ENDPOINTS[key]) {
            console.error(`Unknown setting key: ${key}`);
            return null;
          }
          const value = get().values[key];
          // Return the stored value if available, otherwise use default
          return value !== undefined ? value : ENDPOINTS[key].defaultValue;
        },

        updateSetting: async (key: string, data: any) => {
          if (!ENDPOINTS[key]) {
            console.error(`Unknown setting key: ${key}`);
            return false;
          }

          if (!ENDPOINTS[key].updateEndpoint) {
            console.error(`Setting ${key} is read-only`);
            return false;
          }

          set((state) => ({
            loading: { ...state.loading, [key]: true },
            errors: { ...state.errors, [key]: null },
          }));

          try {
            const endpoint = ENDPOINTS[key].updateEndpoint;
            const payload = transformPayload(key, data);

            const res = await fetch(`${API_BASE}${endpoint}`, {
              method: "POST",
              headers: baseHeaders(),
              body: JSON.stringify(payload),
            });

            if (!res.ok) {
              const errorText = await res.text();
              throw new Error(
                `Failed to update ${key}: ${res.statusText}. ${errorText}`
              );
            }

            const localValue = extractLocalValue(key, data);

            set((state) => ({
              values: { ...state.values, [key]: localValue },
              lastFetched: { ...state.lastFetched, [key]: Date.now() },
              loading: { ...state.loading, [key]: false },
              errors: { ...state.errors, [key]: null },
            }));

            return true;
          } catch (error: any) {
            console.error(`Error updating setting ${key}:`, error);

            set((state) => ({
              loading: { ...state.loading, [key]: false },
              errors: { ...state.errors, [key]: error.message },
            }));

            return false;
          }
        },

        isLoading: (key: string) => get().loading[key] || false,

        getError: (key: string) => get().errors[key] || null,

        initializeSetting: (key: string, value: SettingValue) => {
          if (!ENDPOINTS[key]) {
            console.error(
              `Attempted to initialize unknown setting key: ${key}`
            );
            return;
          }
          set((state) => ({
            values: { ...state.values, [key]: value },
            lastFetched: { ...state.lastFetched, [key]: Date.now() },
            errors: { ...state.errors, [key]: null },
            loading: { ...state.loading, [key]: false },
            _hasHydrated: true, // Mark as hydrated when settings are initialized
          }));
        },

        clearAllSettings: () => {
          set({
            values: Object.fromEntries(
              Object.entries(ENDPOINTS).map(([key, config]) => [
                key,
                config.defaultValue,
              ])
            ),
            lastFetched: {},
            loading: {},
            errors: {},
          });
        },

        setHasHydrated: (state: boolean) => {
          set({ _hasHydrated: state });
        },
      };
    },
    {
      name: "system-settings",
      partialize: (state) => ({
        // Only include serializable data properties
        values: state.values,
        lastFetched: state.lastFetched,
        refreshInterval: state.refreshInterval,
        loading: state.loading,
        errors: state.errors,
        pendingUpdates: state.pendingUpdates,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.setHasHydrated(true);
        }
      },
    }
  )
);

export const useTabNames = (): TabNames =>
  useSystemSettingsStore((state) => state.getSetting("tab-names") as TabNames);

export const useForceInvoiceLogging = (): boolean =>
  useSystemSettingsStore(
    (state) => state.getSetting("force-invoice-logging") as boolean
  );

export const useInvoiceLogging = (): boolean =>
  useSystemSettingsStore(
    (state) => state.getSetting("invoice-logging") as boolean
  );

export const useRexorLinkage = (): boolean =>
  useSystemSettingsStore(
    (state) => state.getSetting("rexor-linkage") as boolean
  );

export const useDocumentDraftingEnabled = (): DocumentDraftingSettings =>
  useSystemSettingsStore(
    (state) => state.getSetting("document-drafting") as DocumentDraftingSettings
  );

export const useRequestLegalAssistance = (): RequestLegalAssistanceSettings =>
  useSystemSettingsStore(
    (state) =>
      state.getSetting(
        "request-legal-assistance"
      ) as RequestLegalAssistanceSettings
  );

export const useColorPalette = (): string =>
  useSystemSettingsStore(
    (state) => state.getSetting("color-palette") as string
  );

export const useFeedbackEnabled = (): FeedbackSettings =>
  useSystemSettingsStore(
    (state) => state.getSetting("feedback-enabled") as FeedbackSettings
  );

export const useUniversityMode = (): boolean =>
  useSystemSettingsStore(
    (state) => state.getSetting("university-mode") as boolean
  );

export const useSystemLanguage = (): string =>
  useSystemSettingsStore(
    (state) => state.getSetting("system-language") as string
  );

export const useSlackSettings = (): SlackSettings =>
  useSystemSettingsStore(
    (state) => state.getSetting("slack-settings") as SlackSettings
  );

export const useSettingsHydrated = (): boolean =>
  useSystemSettingsStore((state) => state._hasHydrated);

export const useCustomAppName = (): string =>
  useSystemSettingsStore(
    (state) => state.getSetting("customAppName") as string
  );

export default useSystemSettingsStore;
