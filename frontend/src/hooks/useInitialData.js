import { useState, useEffect } from "react";
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import useSystemSettingsStore from "@/stores/settingsStore";
import useWorkspaceStore from "@/stores/workspaceStore";

/**
 * Optimized hook to fetch only essential initial data on app load
 * This replaces multiple API calls with a single batched request
 */
export function useInitialData() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const initializeSetting = useSystemSettingsStore(
    (state) => state.initializeSetting
  );
  const setTabNames = useSystemSettingsStore((state) => state.setTabNames);
  const setHasLoadedInitialData = useSystemSettingsStore(
    (state) => state.setHasLoadedInitialData
  );

  useEffect(() => {
    let mounted = true;

    async function fetchInitialData() {
      try {
        const response = await fetch(`${API_BASE}/initial-data`, {
          headers: baseHeaders(),
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch initial data: ${response.status}`);
        }

        const { success, data, error: apiError } = await response.json();

        if (!mounted) return;

        if (!success) {
          throw new Error(apiError || "Failed to fetch initial data");
        }

        // Update stores with the fetched data
        if (data.settings) {
          // Initialize essential settings
          if (data.settings.palette !== undefined) {
            initializeSetting("color-palette", data.settings.palette);
          }

          if (data.settings.language && !localStorage.getItem("language")) {
            localStorage.setItem("language", data.settings.language);
          }

          if (data.settings.tabNames) {
            setTabNames(data.settings.tabNames);
          }

          // Store other settings as needed
          useSystemSettingsStore.getState().setMultipleSettings(data.settings);
        }

        // Update workspace store with populated workspaces
        if (data.workspaces) {
          useWorkspaceStore.getState().setPopulatedWorkspaces(data.workspaces);
        }

        setLoading(false);
        setError(null);
        setHasLoadedInitialData(true);
      } catch (err) {
        console.error("Error fetching initial data:", err);
        if (mounted) {
          setError(err.message);
          setLoading(false);
        }
      }
    }

    fetchInitialData();

    return () => {
      mounted = false;
    };
  }, []); // Only run once on mount

  return { loading, error };
}

/**
 * Hook to check if we need to fetch initial data
 * This prevents unnecessary API calls if data is already cached
 */
export function useNeedsInitialData() {
  const hasSettings = useSystemSettingsStore(
    (state) => Object.keys(state.settings).length > 0
  );

  const hasWorkspaces = useWorkspaceStore(
    (state) => state.populatedWorkspaces.length > 0
  );

  // Only fetch if we don't have essential data
  return !hasSettings || !hasWorkspaces;
}
